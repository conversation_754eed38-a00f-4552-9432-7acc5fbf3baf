//@version=5
indicator("Makucha<PERSON>'s Trade Tools - Pivots & Crossovers", overlay=true)

// ——— PROFESSIONAL VISUAL DESIGN IMPLEMENTATION ————————————————————————————————————————
// ✅ FIXED: Color harmony violations (orange+yellow conflict resolved)
// ✅ FIXED: Visual hierarchy breakdown (price action now primary focus)
// ✅ FIXED: Information density overload (reduced pivot frequency, subtle warnings)
// ✅ FIXED: Poor contrast ratios (WCAG 2.1 AA compliant color schemes)
// ✅ FIXED: Visual clutter (professional mode with 98% warning transparency)
// ✅ FIXED: Competing attention elements (cool blue pivot scheme, minimal borders)
//
// Professional Mode Features:
// • Cool blue color scheme (eliminates warm color conflicts)
// • 98% transparent warnings (functional but invisible)
// • Reduced pivot marker frequency (significant levels only)
// • Thinner borders and smaller markers (cleaner appearance)
// • Optimized for active trading decision-making
// ————————————————————————————————————————————————————————————————————————————————————————

// ——— Inputs - Optimized for Signal Visibility and Detection ————————————————————————————————————————
pivotLookup       = input.int(title="Pivot Lookup", defval=5, minval=1, maxval=50, group="Pivots", tooltip="Increased default for better signal detection")
showPivotShapes   = input.bool(title="Show Pivot Markers", defval=false, group="Pivots", tooltip="Professional default: OFF for cleaner charts")
showBreakoutBoxes = input.bool(title="Show Breakout Signals", defval=true, group="Breakout Signals")
showPatterns      = input.bool(title="Show Reversal Patterns", defval=false, group="Pattern Detection", tooltip="Professional default: OFF to reduce visual clutter")
boxLength         = input.int(title="Signal Length", defval=8, minval=3, maxval=20, group="Breakout Signals", inline="Box", tooltip="Professional default: 8 bars")
boxTransparency   = input.int(title="Signal Transparency", defval=75, minval=50, maxval=95, group="Breakout Signals", inline="Box", tooltip="Optimized for visibility: 75% (was 92%)")

// ——— Signal Debugging and Visibility Controls ————————————————————————————————————————
debugGroup = "🔍 Signal Debugging"
showDebugInfo     = input.bool(title="Show Debug Information", defval=false, group=debugGroup, tooltip="Display signal detection status and conditions")
forceSignalVisibility = input.bool(title="Force Signal Visibility", defval=false, group=debugGroup, tooltip="Override professional mode transparency for testing")
testMode          = input.bool(title="Test Mode", defval=false, group=debugGroup, tooltip="Disable all filters for maximum signal generation")

// ——— Visual Hierarchy Controls ————————————————————————————————————————
hierarchyGroup = "🎯 Display Hierarchy"
showSignalStatus = input.bool(title="Signal Status Panel", defval=true, group=hierarchyGroup, tooltip="PRIMARY: Current signal status and trade decision")
showTradeDecision = input.bool(title="Trade Decision Light", defval=true, group=hierarchyGroup, tooltip="PRIMARY: Traffic light trade decision indicator")
showExecutionGuide = input.bool(title="Execution Guide", defval=false, group=hierarchyGroup, tooltip="SECONDARY: Trade execution and risk management panel")
showGoldDashboard = input.bool(title="Gold Dashboard", defval=false, group=hierarchyGroup, tooltip="SECONDARY: XAUUSD optimization metrics")

// ——— Integration Testing Controls ————————————————————————————————————————
integrationGroup = "🧪 Integration Testing"
enableIntegrationTests = input.bool(title="Enable Integration Tests", defval=false, group=integrationGroup, tooltip="Run comprehensive component integration tests")
showIntegrationDashboard = input.bool(title="Show Integration Dashboard", defval=false, group=integrationGroup, tooltip="Display integration test results and component health")
testSignalPerformanceSync = input.bool(title="Test Signal-Performance Sync", defval=false, group=integrationGroup, tooltip="Validate signal generation triggers performance tracking correctly")
testFilterInteractions = input.bool(title="Test Filter Interactions", defval=false, group=integrationGroup, tooltip="Check for filter interference and side effects")
testStateConsistency = input.bool(title="Test State Consistency", defval=false, group=integrationGroup, tooltip="Validate state synchronization between components")

// ——— XAUUSD Gold Trading Optimization ————————————————————————————————————————
goldGroup = "🥇 XAUUSD Gold Trading"
enableGoldMode = input.bool(title="Enable XAUUSD Gold Mode", defval=false, group=goldGroup, tooltip="Activate Gold-specific optimizations for aggressive small account growth")
goldAggressiveTargets = input.bool(title="Aggressive Gold Targets", defval=true, group=goldGroup, tooltip="Use 3.0-4.5x ATR multipliers for Gold's explosive moves")
goldSessionOptimization = input.bool(title="Session-Aware Trading", defval=true, group=goldGroup, tooltip="Optimize for London/NY sessions, reduce Asian session activity")
goldVolatilityAdaptation = input.bool(title="Dynamic Volatility Scaling", defval=true, group=goldGroup, tooltip="Adapt targets based on Gold's volatility regimes")
goldAntiWhipsaw = input.bool(title="Anti-Whipsaw Protection", defval=true, group=goldGroup, tooltip="Enhanced confirmation to avoid Gold's brutal false breakouts")

// XAUUSD-Specific ATR Multipliers
goldATRAsian = input.float(title="Asian Session ATR Multiplier", defval=2.0, minval=1.0, maxval=5.0, step=0.1, group=goldGroup, tooltip="Conservative targets for low-liquidity Asian session")
goldATRLondon = input.float(title="London Session ATR Multiplier", defval=3.5, minval=2.0, maxval=6.0, step=0.1, group=goldGroup, tooltip="Aggressive targets for high-volatility London session")
goldATROverlap = input.float(title="London/NY Overlap ATR Multiplier", defval=4.5, minval=3.0, maxval=7.0, step=0.1, group=goldGroup, tooltip="Maximum targets for peak volatility overlap period")
goldATRNewYork = input.float(title="New York Session ATR Multiplier", defval=3.0, minval=2.0, maxval=5.0, step=0.1, group=goldGroup, tooltip="Strong targets for NY session momentum")

// Gold Volatility Regime Multipliers
goldHighVolMultiplier = input.float(title="High Volatility Boost", defval=1.3, minval=1.0, maxval=2.0, step=0.1, group=goldGroup, tooltip="Additional multiplier during high volatility periods")
goldLowVolMultiplier = input.float(title="Low Volatility Reduction", defval=0.7, minval=0.5, maxval=1.0, step=0.1, group=goldGroup, tooltip="Reduce targets during low volatility periods")

// ——— Market Quality Filter Inputs (Optimized for Signal Visibility) ————————————————————————————————————————
var mqGroup = "Market Quality Filters"
enableTrendFilter    = input.bool(title="Enable Trend Uncertainty Filter", defval=false, group=mqGroup, tooltip="OPTIMIZED: Default OFF for more signals")
enableRangeFilter    = input.bool(title="Enable Range-Bound Filter", defval=false, group=mqGroup, tooltip="OPTIMIZED: Default OFF for more signals")
enableStrengthFilter = input.bool(title="Enable Breakout Strength Filter", defval=false, group=mqGroup, tooltip="Requires stronger candles for breakout confirmation")
showWarnings        = input.bool(title="Show Market Quality Warnings", defval=true, group=mqGroup, tooltip="Display subtle visual warnings for poor market conditions")

// Filter sensitivity settings (More permissive defaults)
trendUncertaintyThreshold = input.float(title="Trend Uncertainty Threshold (%)", defval=1.0, minval=0.1, maxval=2.0, step=0.1, group=mqGroup, tooltip="OPTIMIZED: Higher threshold = fewer blocks")
consolidationBars        = input.int(title="Consolidation Period (bars)", defval=15, minval=5, maxval=20, group=mqGroup, tooltip="OPTIMIZED: Longer period = fewer blocks")
strengthMultiplier       = input.float(title="Breakout Strength Multiplier", defval=1.2, minval=1.0, maxval=3.0, step=0.1, group=mqGroup, tooltip="OPTIMIZED: Lower requirement = more signals")

// ——— Professional Visual Design Settings ————————————————————————————————————————
var visualGroup = "🎨 Professional Visual Design"
pivotVisibility     = input.int(title="Pivot Marker Frequency", defval=3, minval=1, maxval=4, group=visualGroup, tooltip="1=All pivots, 2=Major pivots, 3=Significant only, 4=Critical only")
warningIntensity    = input.int(title="Warning Background Intensity", defval=98, minval=95, maxval=99, group=visualGroup, tooltip="Higher = more subtle (recommended: 98)")
professionalMode    = input.bool(title="Professional Mode", defval=true, group=visualGroup, tooltip="Optimized color scheme and reduced visual noise")
pivotColorScheme    = input.string(title="Pivot Color Scheme", defval="Cool Blue", options=["Cool Blue", "Classic Orange", "Monochrome"], group=visualGroup, tooltip="Professional color schemes")

// ——— Global Market Quality Variables (declared at global scope) ————————————————————————————————————————
var bool trendUncertain = false
var int consolidationCount = 0
var bool isRangeBound = false
var bool poorMarketQuality = false

// ——— Signal Performance Tracking Variables ————————————————————————————————————————
var int lastSignalBar = na
var float lastSignalPrice = na
var string lastSignalDirection = na
var float lastSignalTarget = na
var bool lastSignalActive = false
var bool lastSignalSuccess = false
var int totalSignals = 0
var int successfulSignals = 0
var float avgSignalReturn = 0.0

// ——— Integration Test State Variables ————————————————————————————————————————
var int integrationTestCount = 0
var int integrationTestPassed = 0
var int integrationTestFailed = 0
var string lastIntegrationError = na
var bool signalPerformanceSyncOK = true
var bool filterInteractionOK = true
var bool stateConsistencyOK = true
var bool visualDataSyncOK = true

// Integration test tracking
var int lastBullishSignalBar = na
var int lastBearishSignalBar = na
var bool lastSignalTriggeredTracking = false
var bool lastTrackingTriggeredVisuals = false

// Performance tracking settings
performanceGroup = "📊 Signal Performance"
enablePerformanceTracking = input.bool(title="Enable Performance Tracking", defval=true, group=performanceGroup, tooltip="Track signal success/failure rates")
showPerformanceTable = input.bool(title="Show Performance Table", defval=true, group=performanceGroup, tooltip="Display performance statistics")
targetMethod = input.string(title="Target Calculation Method", defval="ATR-based", options=["ATR-based", "Percentage", "Next Pivot"], group=performanceGroup, tooltip="How to calculate signal targets")
targetMultiplier = input.float(title="Target Multiplier", defval=2.0, minval=0.5, maxval=5.0, step=0.1, group=performanceGroup, tooltip="Multiplier for target calculation")
maxSignalDuration = input.int(title="Max Signal Duration (bars)", defval=20, minval=5, maxval=100, group=performanceGroup, tooltip="Maximum bars to track a signal")

// Pivot sources are hardcoded to standard values for simplicity
// pivotHighSource = high, pivotLowSource = low

// ——— Helper Functions ——————————————————————————————————
isUp(index) =>
    close[index] > open[index]

isDown(index) =>
    close[index] < open[index]



// ——— Pivot Detection ——————————————————————————————————
hih         = ta.pivothigh(high, pivotLookup, pivotLookup)
lol         = ta.pivotlow(low,   pivotLookup, pivotLookup)

// ——— Pre-calculate ATR for performance and consistency ————————————————————————————————————————
atrValue = ta.atr(14)  // Calculate once at global scope for use in target calculations

// ——— XAUUSD Session Detection and Analysis ————————————————————————————————————————
// Session time definitions (UTC)
asianSession = time(timeframe.period, "0000-0800", "UTC")
londonSession = time(timeframe.period, "0800-1600", "UTC")
newYorkSession = time(timeframe.period, "1300-2100", "UTC")
londonNYOverlap = time(timeframe.period, "1300-1600", "UTC")

// Current session detection
isAsianSession = not na(asianSession)
isLondonSession = not na(londonSession)
isNewYorkSession = not na(newYorkSession)
isOverlapSession = not na(londonNYOverlap)

// Session quality scoring for XAUUSD
sessionQuality = switch
    isOverlapSession => 1.2    // Maximum opportunity - London/NY overlap
    isLondonSession => 1.0     // High quality - London session
    isNewYorkSession => 0.8    // Good quality - NY session
    isAsianSession => 0.3      // Low quality - Asian session
    => 0.5                     // Default/weekend

// XAUUSD-optimized ATR multiplier calculation
goldATRMultiplier = if enableGoldMode
    // Base multiplier by session
    baseMultiplier = switch
        isOverlapSession => goldATROverlap    // 4.5x default
        isLondonSession => goldATRLondon      // 3.5x default
        isNewYorkSession => goldATRNewYork    // 3.0x default
        isAsianSession => goldATRAsian        // 2.0x default
        => 2.5                                // Default fallback

    baseMultiplier
else
    targetMultiplier  // Use standard multiplier when Gold mode disabled

// Current session name for display
currentSessionName = switch
    isOverlapSession => "OVERLAP"
    isLondonSession => "LONDON"
    isNewYorkSession => "NEW YORK"
    isAsianSession => "ASIAN"
    => "CLOSED"

// ——— XAUUSD Volatility Regime Detection ————————————————————————————————————————
// Calculate volatility regime for dynamic target scaling
atr20Average = ta.sma(atrValue, 20)
volatilityRatio = atrValue / atr20Average

// Volatility regime classification
volatilityRegime = switch
    volatilityRatio >= 1.5 => "HIGH"      // ATR 50% above average
    volatilityRatio <= 0.7 => "LOW"       // ATR 30% below average
    => "NORMAL"                           // ATR within normal range

// Dynamic volatility multiplier for Gold mode
volatilityMultiplier = if enableGoldMode and goldVolatilityAdaptation
    switch volatilityRegime
        "HIGH" => goldHighVolMultiplier    // 1.3x default boost
        "LOW" => goldLowVolMultiplier      // 0.7x default reduction
        => 1.0                             // No adjustment for normal
else
    1.0  // No volatility adjustment when disabled

// Final XAUUSD-optimized ATR multiplier
finalATRMultiplier = goldATRMultiplier * volatilityMultiplier

// Volatility regime color for display
volatilityColor = switch volatilityRegime
    "HIGH" => color.red
    "LOW" => color.blue
    => color.gray

// The “top” and “bottom” lines refer to pivot high and low values
// Enhanced pivot levels using close prices for better cross-broker reliability
var float pivotHighLevel = na
var float pivotLowLevel = na
var bool newPivotHigh = false
var bool newPivotLow = false

// Update pivot levels only when new pivots are confirmed
if not na(hih)
    pivotHighLevel := close[pivotLookup]  // Use close price instead of high for reliability
    newPivotHigh := true
else
    newPivotHigh := false

if not na(lol)
    pivotLowLevel := close[pivotLookup]   // Use close price instead of low for reliability
    newPivotLow := true
else
    newPivotLow := false

// Legacy variables for compatibility (but using more reliable close-based levels)
top = pivotHighLevel
bottom = pivotLowLevel

// ——— Streamlined Pivot Visualization for Trend Direction ————————————————————————————————————
// Clean support/resistance levels that complement trend indicators
plot(
     pivotHighLevel,
     offset = -pivotLookup,
     linewidth = 1,
     color = pivotHighLevel != pivotHighLevel[1] ? na : color.new(color.orange, 70),
     title = "Resistance Level",
     display = display.all
 )

plot(
     pivotLowLevel,
     offset = -pivotLookup,
     linewidth = 1,
     color = pivotLowLevel != pivotLowLevel[1] ? na : color.new(color.blue, 70),
     title = "Support Level",
     display = display.all
 )

// ——— Professional Pivot Marker System ————————————————————————————————————————
// Dynamic color scheme based on user preference
pivotHighColor = professionalMode ?
     (pivotColorScheme == "Cool Blue" ? color.new(color.blue, 60) :
      pivotColorScheme == "Monochrome" ? color.new(color.gray, 50) :
      color.new(color.orange, 50)) : color.new(color.orange, 30)

pivotLowColor = professionalMode ?
     (pivotColorScheme == "Cool Blue" ? color.new(color.blue, 60) :
      pivotColorScheme == "Monochrome" ? color.new(color.gray, 50) :
      color.new(color.blue, 50)) : color.new(color.blue, 30)

// Frequency-based pivot filtering for reduced visual noise
showThisPivotHigh = showPivotShapes and newPivotHigh and
     (pivotVisibility == 1 or
      (pivotVisibility == 2 and bar_index % 2 == 0) or
      (pivotVisibility == 3 and bar_index % 3 == 0) or
      (pivotVisibility == 4 and bar_index % 4 == 0))

showThisPivotLow = showPivotShapes and newPivotLow and
     (pivotVisibility == 1 or
      (pivotVisibility == 2 and bar_index % 2 == 0) or
      (pivotVisibility == 3 and bar_index % 3 == 0) or
      (pivotVisibility == 4 and bar_index % 4 == 0))

// Professional pivot markers with Pine Script v5 compliant constant sizes
// Professional mode: tiny markers for clean appearance
plotshape(
     series = professionalMode ? showThisPivotHigh : na,
     title = "Resistance Level (Professional)",
     location = location.abovebar,
     color = pivotHighColor,
     style = shape.triangledown,
     size = size.tiny,
     offset = -pivotLookup
 )

plotshape(
     series = professionalMode ? showThisPivotLow : na,
     title = "Support Level (Professional)",
     location = location.belowbar,
     color = pivotLowColor,
     style = shape.triangleup,
     size = size.tiny,
     offset = -pivotLookup
 )

// Standard mode: small markers for better visibility
plotshape(
     series = not professionalMode ? showThisPivotHigh : na,
     title = "Resistance Level (Standard)",
     location = location.abovebar,
     color = pivotHighColor,
     style = shape.triangledown,
     size = size.small,
     offset = -pivotLookup
 )

plotshape(
     series = not professionalMode ? showThisPivotLow : na,
     title = "Support Level (Standard)",
     location = location.belowbar,
     color = pivotLowColor,
     style = shape.triangleup,
     size = size.small,
     offset = -pivotLookup
 )

// ——— Enhanced Breakout Detection with Reliable Logic —————————————————
// Only create boxes on actual breakouts with proper confirmation
var float lastBreakoutHigh = na
var float lastBreakoutLow = na

// Enhanced breakout detection with market quality filters
// ATR-based volatility adjustment
atr14 = ta.atr(14)
volatilityAdjustment = atr14 / close * 100

// Breakout strength validation (when enabled)
var bool strongBreakout = true
if enableStrengthFilter
    // Calculate average candle size over last 10 bars
    avgCandleSize = 0.0
    for i = 1 to 10
        avgCandleSize += math.abs(close[i] - open[i])
    avgCandleSize := avgCandleSize / 10

    // Current candle size
    currentCandleSize = math.abs(close - open)

    // Strong if current candle is larger than required multiple of average
    strongBreakout := currentCandleSize >= (avgCandleSize * strengthMultiplier)
else
    strongBreakout := true

// 2-bar follow-through validation
var bool followThroughBull = true
var bool followThroughBear = true
if enableStrengthFilter
    // For bullish: price stayed above breakout level for 2 bars
    followThroughBull := close >= pivotHighLevel and close[1] >= pivotHighLevel
    // For bearish: price stayed below breakout level for 2 bars
    followThroughBear := close <= pivotLowLevel and close[1] <= pivotLowLevel
else
    followThroughBull := true
    followThroughBear := true

// ——— Enhanced Bullish Breakout Detection with Debugging ————————————————————————————————————————
// Individual condition checks for debugging
basicConditions = showBreakoutBoxes and not na(pivotHighLevel)
crossoverCondition = ta.crossover(close, pivotHighLevel)
duplicateCheck = na(lastBreakoutHigh) or pivotHighLevel != lastBreakoutHigh
trendFilterPass = testMode or not enableTrendFilter or not trendUncertain
rangeFilterPass = testMode or not enableRangeFilter or not isRangeBound
strengthFilterPass = testMode or not enableStrengthFilter or strongBreakout

// ——— XAUUSD Gold-Specific Entry Confirmation ————————————————————————————————————————
// Volume spike confirmation for Gold breakouts
volumeAverage = ta.sma(volume, 10)
volumeSpike = volume > volumeAverage * 1.5

// Momentum continuation check for Gold
momentumConfirmation = close > high[1]  // Price continuing above previous high

// Session quality filter for Gold trading
sessionQualityPass = not enableGoldMode or not goldSessionOptimization or sessionQuality >= 0.8

// Anti-whipsaw protection for Gold
goldAntiWhipsawPass = if enableGoldMode and goldAntiWhipsaw
    // Require volume spike AND momentum continuation for Gold
    volumeSpike and momentumConfirmation
else
    true  // No additional confirmation when disabled

// ——— Enhanced News-Aware Trading Controls for XAUUSD ————————————————————————————————————————
// Major news times that significantly impact Gold (UTC times)
newsHour = hour(time)
newsMinute = minute(time)
dayOfWeek = dayofweek(time)

// High-impact news events for Gold (UTC times)
// 8:30 AM UTC - US Economic Data (CPI, NFP, etc.)
// 12:30 PM UTC - Fed announcements
// 2:00 PM UTC - FOMC meetings
// 6:00 PM UTC - US market close volatility
isUSDataTime = newsHour == 8 and newsMinute >= 15 and newsMinute <= 45
isFedTime = newsHour == 12 and newsMinute >= 15 and newsMinute <= 45
isFOMCTime = newsHour == 14 and newsMinute >= 0 and newsMinute <= 30
isMarketCloseTime = newsHour == 18 and newsMinute >= 0 and newsMinute <= 30

// Friday close volatility (avoid last 2 hours of Friday)
isFridayClose = dayOfWeek == 6 and newsHour >= 19

// Combined news time detection
isHighImpactNewsTime = isUSDataTime or isFedTime or isFOMCTime or isMarketCloseTime or isFridayClose

// News avoidance filter
newsTimePass = not enableGoldMode or not isHighImpactNewsTime

// Volatility spike detection (potential news reaction)
volatilitySpike = atrValue > atr20Average * 2.0  // ATR 100% above average
newsVolatilityPass = not enableGoldMode or not volatilitySpike

// Combined Gold entry confirmation
goldEntryConfirmation = goldAntiWhipsawPass and sessionQualityPass and newsTimePass and newsVolatilityPass

// Combined bullish breakout condition
bullishBreakout = basicConditions and crossoverCondition and duplicateCheck and
                 trendFilterPass and rangeFilterPass and strengthFilterPass and
                 goldEntryConfirmation

// ——— Integration Test: Filter System Interactions ————————————————————————————————————————
if enableIntegrationTests and testFilterInteractions
    integrationTestCount += 1

    // Test 3: Verify filter independence (no unexpected interactions)
    filterTestPassed = true

    // Check if test mode properly bypasses all filters
    if testMode
        if not (trendFilterPass and rangeFilterPass and strengthFilterPass)
            filterTestPassed := false
            lastIntegrationError := "Test mode failed to bypass all filters"

    // Check filter precedence and logic
    if enableTrendFilter and enableRangeFilter
        // Both filters enabled - check for proper AND logic
        expectedResult = (not trendUncertain) and (not isRangeBound)
        actualResult = trendFilterPass and rangeFilterPass
        if expectedResult != actualResult
            filterTestPassed := false
            lastIntegrationError := "Filter AND logic inconsistency detected"

    if filterTestPassed
        filterInteractionOK := true
        integrationTestPassed += 1
    else
        filterInteractionOK := false
        integrationTestFailed += 1

if bullishBreakout
    // Enhanced visibility for signal detection
    signalTransparency = forceSignalVisibility ? 50 : (professionalMode ? boxTransparency + 3 : boxTransparency)

    // Professional breakout box design with improved visibility
    professionalGreen = professionalMode ? color.new(color.teal, signalTransparency) : color.new(color.green, signalTransparency)
    professionalBorder = professionalMode ? color.new(color.teal, math.max(signalTransparency - 15, 50)) : color.new(color.green, 70)

    box.new(
         left        = bar_index,
         top         = pivotHighLevel,
         right       = bar_index + boxLength,
         bottom      = pivotLowLevel,
         bgcolor     = professionalGreen,
         border_color= professionalBorder,
         border_width= professionalMode ? 2 : 3  // Increased visibility
     )
    lastBreakoutHigh := pivotHighLevel

    // ——— Signal Performance Tracking for Bullish Breakout ————————————————————————————————————————
    if enablePerformanceTracking
        // Calculate target based on selected method (XAUUSD-optimized)
        effectiveMultiplier = enableGoldMode ? finalATRMultiplier : targetMultiplier
        signalTarget = switch targetMethod
            "ATR-based" => pivotHighLevel + (atrValue * effectiveMultiplier)
            "Percentage" => pivotHighLevel * (1 + effectiveMultiplier / 100)
            "Next Pivot" => na(pivotLowLevel) ? pivotHighLevel * 1.02 : pivotHighLevel + math.abs(pivotHighLevel - pivotLowLevel)
            => pivotHighLevel * 1.02  // Default fallback

        // Record signal details
        lastSignalBar := bar_index
        lastSignalPrice := close
        lastSignalDirection := "BULLISH"
        lastSignalTarget := signalTarget
        lastSignalActive := true
        lastSignalSuccess := false
        totalSignals += 1

        // ——— Integration Test: Signal-to-Performance Sync ————————————————————————————————————————
        if enableIntegrationTests and testSignalPerformanceSync
            integrationTestCount += 1
            lastBullishSignalBar := bar_index

            // Test 1: Verify performance tracking was triggered
            if lastSignalActive and lastSignalDirection == "BULLISH" and not na(lastSignalTarget)
                signalPerformanceSyncOK := true
                lastSignalTriggeredTracking := true
                integrationTestPassed += 1
            else
                signalPerformanceSyncOK := false
                lastIntegrationError := "Bullish signal failed to trigger performance tracking"
                integrationTestFailed += 1

// ——— CLEAR BUY SIGNAL MARKERS ————————————————————————————————————————
// Add prominent BUY signal marker for clarity
plotshape(
    series = bullishBreakout,
    title = "🟢 BUY SIGNAL - Enter at Box Top",
    location = location.belowbar,
    color = color.new(color.lime, 0),
    style = shape.labelup,
    size = size.large,
    text = "BUY\nENTER NOW",
    textcolor = color.black
)

// Add entry price line for exact entry point
var line buyEntryLine = na
if bullishBreakout
    // Draw entry line at the top of the breakout box
    buyEntryLine := line.new(
        x1 = bar_index,
        y1 = pivotHighLevel,
        x2 = bar_index + 5,
        y2 = pivotHighLevel,
        color = color.new(color.lime, 30),
        width = 3,
        style = line.style_solid
    )
    // Add entry price label
    label.new(
        x = bar_index + 2,
        y = pivotHighLevel,
        text = "ENTRY: " + str.tostring(pivotHighLevel, "#.##"),
        color = color.new(color.lime, 20),
        textcolor = color.black,
        style = label.style_label_down,
        size = size.normal
    )

// ——— Enhanced Bearish Breakdown Detection with Debugging ————————————————————————————————————————
// Individual condition checks for debugging
basicConditionsBear = showBreakoutBoxes and not na(pivotLowLevel)
crossunderCondition = ta.crossunder(close, pivotLowLevel)
duplicateCheckBear = na(lastBreakoutLow) or pivotLowLevel != lastBreakoutLow
trendFilterPassBear = testMode or not enableTrendFilter or not trendUncertain
rangeFilterPassBear = testMode or not enableRangeFilter or not isRangeBound
strengthFilterPassBear = testMode or not enableStrengthFilter or strongBreakout

// Combined bearish breakdown condition (with Gold confirmation)
bearishBreakdown = basicConditionsBear and crossunderCondition and duplicateCheckBear and
                  trendFilterPassBear and rangeFilterPassBear and strengthFilterPassBear and
                  goldEntryConfirmation

if bearishBreakdown
    // Enhanced visibility for signal detection
    signalTransparencyBear = forceSignalVisibility ? 50 : (professionalMode ? boxTransparency + 3 : boxTransparency)

    // Professional breakout box design with improved visibility
    professionalRed = professionalMode ? color.new(color.maroon, signalTransparencyBear) : color.new(color.red, signalTransparencyBear)
    professionalRedBorder = professionalMode ? color.new(color.maroon, math.max(signalTransparencyBear - 15, 50)) : color.new(color.red, 70)

    box.new(
         left        = bar_index,
         top         = pivotHighLevel,
         right       = bar_index + boxLength,
         bottom      = pivotLowLevel,
         bgcolor     = professionalRed,
         border_color= professionalRedBorder,
         border_width= professionalMode ? 2 : 3  // Increased visibility
     )
    lastBreakoutLow := pivotLowLevel

    // ——— Signal Performance Tracking for Bearish Breakdown ————————————————————————————————————————
    if enablePerformanceTracking
        // Calculate target based on selected method (XAUUSD-optimized)
        effectiveMultiplierBear = enableGoldMode ? finalATRMultiplier : targetMultiplier
        signalTargetBear = switch targetMethod
            "ATR-based" => pivotLowLevel - (atrValue * effectiveMultiplierBear)
            "Percentage" => pivotLowLevel * (1 - effectiveMultiplierBear / 100)
            "Next Pivot" => na(pivotHighLevel) ? pivotLowLevel * 0.98 : pivotLowLevel - math.abs(pivotHighLevel - pivotLowLevel)
            => pivotLowLevel * 0.98  // Default fallback

        // Record signal details
        lastSignalBar := bar_index
        lastSignalPrice := close
        lastSignalDirection := "BEARISH"
        lastSignalTarget := signalTargetBear
        lastSignalActive := true
        lastSignalSuccess := false
        totalSignals += 1

        // ——— Integration Test: Signal-to-Performance Sync ————————————————————————————————————————
        if enableIntegrationTests and testSignalPerformanceSync
            integrationTestCount += 1
            lastBearishSignalBar := bar_index

            // Test 2: Verify performance tracking was triggered for bearish signal
            if lastSignalActive and lastSignalDirection == "BEARISH" and not na(lastSignalTarget)
                signalPerformanceSyncOK := true
                lastSignalTriggeredTracking := true
                integrationTestPassed += 1
            else
                signalPerformanceSyncOK := false
                lastIntegrationError := "Bearish signal failed to trigger performance tracking"
                integrationTestFailed += 1

// ——— CLEAR SELL SIGNAL MARKERS ————————————————————————————————————————
// Add prominent SELL signal marker for clarity
plotshape(
    series = bearishBreakdown,
    title = "🔴 SELL SIGNAL - Enter at Box Bottom",
    location = location.abovebar,
    color = color.new(color.red, 0),
    style = shape.labeldown,
    size = size.large,
    text = "SELL\nENTER NOW",
    textcolor = color.white
)

// Add entry price line for exact entry point
var line sellEntryLine = na
if bearishBreakdown
    // Draw entry line at the bottom of the breakout box
    sellEntryLine := line.new(
        x1 = bar_index,
        y1 = pivotLowLevel,
        x2 = bar_index + 5,
        y2 = pivotLowLevel,
        color = color.new(color.red, 30),
        width = 3,
        style = line.style_solid
    )
    // Add entry price label
    label.new(
        x = bar_index + 2,
        y = pivotLowLevel,
        text = "ENTRY: " + str.tostring(pivotLowLevel, "#.##"),
        color = color.new(color.red, 20),
        textcolor = color.white,
        style = label.style_label_up,
        size = size.normal
    )

// ——— Signal Success/Failure Tracking Logic ————————————————————————————————————————
if enablePerformanceTracking and lastSignalActive
    // Check if signal has reached target or expired
    signalAge = bar_index - lastSignalBar

    if lastSignalDirection == "BULLISH"
        if high >= lastSignalTarget
            lastSignalSuccess := true
            lastSignalActive := false
            successfulSignals += 1
        else if signalAge >= maxSignalDuration or close < lastSignalPrice * 0.98  // 2% stop loss
            lastSignalActive := false

    if lastSignalDirection == "BEARISH"
        if low <= lastSignalTarget
            lastSignalSuccess := true
            lastSignalActive := false
            successfulSignals += 1
        else if signalAge >= maxSignalDuration or close > lastSignalPrice * 1.02  // 2% stop loss
            lastSignalActive := false

// ——— Integration Test: State Synchronization Validation ————————————————————————————————————————
if enableIntegrationTests and testStateConsistency
    integrationTestCount += 1

    // Test 5: Check for state inconsistencies
    stateTestPassed = true

    // Validate signal state consistency
    if lastSignalActive
        // Active signal must have all required data
        if na(lastSignalBar) or na(lastSignalPrice) or na(lastSignalDirection) or na(lastSignalTarget)
            stateTestPassed := false
            lastIntegrationError := "Active signal missing required data"

        // Signal age must be reasonable
        if not na(lastSignalBar) and (bar_index - lastSignalBar) < 0
            stateTestPassed := false
            lastIntegrationError := "Invalid signal timing detected"

    // Validate performance tracking consistency
    if enablePerformanceTracking
        if totalSignals < 0 or successfulSignals < 0 or successfulSignals > totalSignals
            stateTestPassed := false
            lastIntegrationError := "Performance tracking counters inconsistent"

    // Validate filter state consistency
    if enableTrendFilter and na(trendUncertain)
        stateTestPassed := false
        lastIntegrationError := "Trend filter enabled but state undefined"

    if stateTestPassed
        stateConsistencyOK := true
        integrationTestPassed += 1
    else
        stateConsistencyOK := false
        integrationTestFailed += 1

// ——— Signal Debug Information Display ————————————————————————————————————————
if showDebugInfo
    // Create debug table showing signal conditions
    var table debugTable = table.new(position.top_right, 2, 8, bgcolor=color.new(color.white, 90), border_width=1)

    if barstate.islast
        table.cell(debugTable, 0, 0, "Signal Debug", text_color=color.black, text_size=size.small)
        table.cell(debugTable, 1, 0, "Status", text_color=color.black, text_size=size.small)

        table.cell(debugTable, 0, 1, "Pivot High", text_color=color.black, text_size=size.tiny)
        table.cell(debugTable, 1, 1, na(pivotHighLevel) ? "N/A" : str.tostring(pivotHighLevel, "#.##"), text_color=color.black, text_size=size.tiny)

        table.cell(debugTable, 0, 2, "Pivot Low", text_color=color.black, text_size=size.tiny)
        table.cell(debugTable, 1, 2, na(pivotLowLevel) ? "N/A" : str.tostring(pivotLowLevel, "#.##"), text_color=color.black, text_size=size.tiny)

        table.cell(debugTable, 0, 3, "Close Price", text_color=color.black, text_size=size.tiny)
        table.cell(debugTable, 1, 3, str.tostring(close, "#.##"), text_color=color.black, text_size=size.tiny)

        table.cell(debugTable, 0, 4, "Trend Filter", text_color=color.black, text_size=size.tiny)
        table.cell(debugTable, 1, 4, trendUncertain ? "BLOCK" : "PASS", text_color=trendUncertain ? color.red : color.green, text_size=size.tiny)

        table.cell(debugTable, 0, 5, "Range Filter", text_color=color.black, text_size=size.tiny)
        table.cell(debugTable, 1, 5, isRangeBound ? "BLOCK" : "PASS", text_color=isRangeBound ? color.red : color.green, text_size=size.tiny)

        table.cell(debugTable, 0, 6, "Last Bull Signal", text_color=color.black, text_size=size.tiny)
        table.cell(debugTable, 1, 6, bullishBreakout ? "NOW!" : "None", text_color=bullishBreakout ? color.green : color.gray, text_size=size.tiny)

        table.cell(debugTable, 0, 7, "Last Bear Signal", text_color=color.black, text_size=size.tiny)
        table.cell(debugTable, 1, 7, bearishBreakdown ? "NOW!" : "None", text_color=bearishBreakdown ? color.red : color.gray, text_size=size.tiny)

// ——— CURRENT SIGNAL STATUS PANEL (Primary Trading Interface) ————————————————————————————————————————
if showSignalStatus
    // Create prominent signal status panel for clear trade decisions
    var table signalStatusTable = table.new(position.top_left, 2, 8,
                                           bgcolor=color.new(color.black, 75),
                                           border_width=2,
                                           border_color=color.new(color.white, 30))

    if barstate.islast
        // Calculate current signal status
        currentSignalType = if lastSignalActive
            lastSignalDirection == "BULLISH" ? "🟢 BUY SIGNAL" : "🔴 SELL SIGNAL"
        else
            "⚪ NO ACTIVE SIGNAL"

        // Calculate entry price (top of breakout box for bullish, bottom for bearish)
        currentEntryPrice = if lastSignalActive
            if lastSignalDirection == "BULLISH"
                na(lastBreakoutHigh) ? lastSignalPrice : lastBreakoutHigh
            else
                na(lastBreakoutLow) ? lastSignalPrice : lastBreakoutLow
        else
            na

        // Calculate stop loss (opposite side of breakout)
        currentStopLoss = if lastSignalActive and not na(currentEntryPrice)
            if lastSignalDirection == "BULLISH"
                // Stop below the breakout low
                na(lastBreakoutLow) ? currentEntryPrice - (atrValue * 1.0) : lastBreakoutLow - (atrValue * 0.5)
            else
                // Stop above the breakout high
                na(lastBreakoutHigh) ? currentEntryPrice + (atrValue * 1.0) : lastBreakoutHigh + (atrValue * 0.5)
        else
            na

        // Calculate take profit (using Gold-optimized multiplier)
        currentTakeProfit = if lastSignalActive and not na(currentEntryPrice)
            effectiveMultiplier = enableGoldMode ? finalATRMultiplier : targetMultiplier
            if lastSignalDirection == "BULLISH"
                currentEntryPrice + (atrValue * effectiveMultiplier)
            else
                currentEntryPrice - (atrValue * effectiveMultiplier)
        else
            na

        // Calculate time remaining (bars since signal)
        barsInSignal = lastSignalActive ? bar_index - lastSignalBar : na
        timeRemaining = lastSignalActive ? maxSignalDuration - barsInSignal : na

        // Header
        table.cell(signalStatusTable, 0, 0, "🎯 CURRENT SIGNAL STATUS",
                  text_color=color.white, text_size=size.normal,
                  bgcolor=color.new(color.blue, 60))
        table.cell(signalStatusTable, 1, 0, "TRADE DECISION",
                  text_color=color.white, text_size=size.normal,
                  bgcolor=color.new(color.blue, 60))

        // Signal Type
        table.cell(signalStatusTable, 0, 1, "Signal Type",
                  text_color=color.white, text_size=size.small)
        signalColor = lastSignalActive ? (lastSignalDirection == "BULLISH" ? color.lime : color.red) : color.gray
        table.cell(signalStatusTable, 1, 1, currentSignalType,
                  text_color=signalColor, text_size=size.small)

        // Entry Price
        table.cell(signalStatusTable, 0, 2, "Entry Price",
                  text_color=color.white, text_size=size.small)
        entryText = na(currentEntryPrice) ? "WAIT FOR SIGNAL" : str.tostring(currentEntryPrice, "#.##")
        table.cell(signalStatusTable, 1, 2, entryText,
                  text_color=na(currentEntryPrice) ? color.gray : color.yellow, text_size=size.small)

        // Stop Loss
        table.cell(signalStatusTable, 0, 3, "Stop Loss",
                  text_color=color.white, text_size=size.small)
        stopText = na(currentStopLoss) ? "N/A" : str.tostring(currentStopLoss, "#.##")
        table.cell(signalStatusTable, 1, 3, stopText,
                  text_color=na(currentStopLoss) ? color.gray : color.orange, text_size=size.small)

        // Take Profit
        table.cell(signalStatusTable, 0, 4, "Take Profit",
                  text_color=color.white, text_size=size.small)
        tpText = na(currentTakeProfit) ? "N/A" : str.tostring(currentTakeProfit, "#.##")
        table.cell(signalStatusTable, 1, 4, tpText,
                  text_color=na(currentTakeProfit) ? color.gray : color.lime, text_size=size.small)

        // Risk:Reward Ratio
        table.cell(signalStatusTable, 0, 5, "Risk:Reward",
                  text_color=color.white, text_size=size.small)
        riskReward = if not na(currentEntryPrice) and not na(currentStopLoss) and not na(currentTakeProfit)
            risk = math.abs(currentEntryPrice - currentStopLoss)
            reward = math.abs(currentTakeProfit - currentEntryPrice)
            risk > 0 ? reward / risk : na
        else
            na
        rrText = na(riskReward) ? "N/A" : str.tostring(riskReward, "#.#") + ":1"
        rrColor = na(riskReward) ? color.gray : (riskReward >= 3.0 ? color.lime : (riskReward >= 2.0 ? color.yellow : color.red))
        table.cell(signalStatusTable, 1, 5, rrText,
                  text_color=rrColor, text_size=size.small)

        // Last Signal Timestamp
        table.cell(signalStatusTable, 0, 6, "Last Signal",
                  text_color=color.white, text_size=size.small)
        lastSignalText = if not na(lastSignalBar)
            barsAgo = bar_index - lastSignalBar
            if barsAgo == 0
                "NOW (Current Bar)"
            else if barsAgo == 1
                "1 bar ago"
            else
                str.tostring(barsAgo) + " bars ago"
        else
            "No signals yet"
        lastSignalColor = if not na(lastSignalBar)
            barsAgo = bar_index - lastSignalBar
            if barsAgo <= 1
                color.lime
            else if barsAgo <= 5
                color.yellow
            else
                color.orange
        else
            color.gray
        table.cell(signalStatusTable, 1, 6, lastSignalText,
                  text_color=lastSignalColor, text_size=size.small)

        // Entry Instructions
        table.cell(signalStatusTable, 0, 7, "Entry Method",
                  text_color=color.white, text_size=size.small)
        entryMethod = lastSignalActive ? "MARKET ORDER NOW" : "WAIT FOR BREAKOUT"
        entryMethodColor = lastSignalActive ? color.lime : color.gray
        table.cell(signalStatusTable, 1, 7, entryMethod,
                  text_color=entryMethodColor, text_size=size.small)

// ——— TRADE DECISION TRAFFIC LIGHT INDICATOR ————————————————————————————————————————
if showTradeDecision
    // Create simplified traffic light decision system
    var table tradeDecisionTable = table.new(position.middle_left, 2, 4,
                                            bgcolor=color.new(color.black, 80),
                                            border_width=3,
                                            border_color=color.new(color.white, 40))

    if barstate.islast
        // Calculate trade decision status
        // GREEN: Active signal with good conditions
        // YELLOW: Potential setup forming, prepare for entry
        // RED: Avoid trading, poor conditions

        // Check for active high-quality signal
        hasActiveSignal = lastSignalActive and timeRemaining > 0

        // Check for good market conditions
        goodConditions = if enableGoldMode
            goldEntryConfirmation and sessionQuality >= 0.8 and not isHighImpactNewsTime
        else
            not trendUncertain and not isRangeBound

        // Check for potential setup (conditions good but no signal yet)
        potentialSetup = not hasActiveSignal and goodConditions and
                        (close > ta.sma(close, 20) or close < ta.sma(close, 20)) // Some momentum

        // Determine traffic light status
        tradeDecision = if hasActiveSignal and goodConditions
            "🟢 ENTER NOW"
        else if potentialSetup or (hasActiveSignal and not goodConditions)
            "🟡 PREPARE"
        else
            "🔴 AVOID"

        // Determine decision color and explanation
        decisionColor = if str.contains(tradeDecision, "🟢")
            color.lime
        else if str.contains(tradeDecision, "🟡")
            color.yellow
        else
            color.red

        // Create explanation for decision
        decisionReason = if str.contains(tradeDecision, "🟢")
            "Active signal + Good conditions"
        else if str.contains(tradeDecision, "🟡")
            if potentialSetup
                "Setup forming, watch for signal"
            else
                "Signal active but conditions poor"
        else
            if enableGoldMode
                if not goldEntryConfirmation
                    "Gold filters blocking entry"
                else if sessionQuality < 0.8
                    "Poor session quality"
                else if isHighImpactNewsTime
                    "High-impact news time"
                else
                    "No signal detected"
            else
                if trendUncertain
                    "Trend uncertainty detected"
                else if isRangeBound
                    "Market in range-bound state"
                else
                    "No signal detected"

        // Header
        table.cell(tradeDecisionTable, 0, 0, "🚦 TRADE DECISION",
                  text_color=color.white, text_size=size.large,
                  bgcolor=color.new(color.navy, 60))
        table.cell(tradeDecisionTable, 1, 0, "",
                  text_color=color.white, text_size=size.large,
                  bgcolor=color.new(color.navy, 60))

        // Main decision
        table.cell(tradeDecisionTable, 0, 1, "Status:",
                  text_color=color.white, text_size=size.normal)
        table.cell(tradeDecisionTable, 1, 1, tradeDecision,
                  text_color=decisionColor, text_size=size.large)

        // Reason
        table.cell(tradeDecisionTable, 0, 2, "Reason:",
                  text_color=color.white, text_size=size.small)
        table.cell(tradeDecisionTable, 1, 2, decisionReason,
                  text_color=color.white, text_size=size.small)

        // Next action
        nextAction = if str.contains(tradeDecision, "🟢")
            "Execute trade immediately"
        else if str.contains(tradeDecision, "🟡")
            "Monitor for signal/improvement"
        else
            "Wait for better conditions"

        table.cell(tradeDecisionTable, 0, 3, "Action:",
                  text_color=color.white, text_size=size.small)
        table.cell(tradeDecisionTable, 1, 3, nextAction,
                  text_color=decisionColor, text_size=size.small)

// ——— TRADE EXECUTION GUIDANCE PANEL ————————————————————————————————————————
if showExecutionGuide and lastSignalActive
    // Create trade execution guidance panel
    var table executionTable = table.new(position.middle_right, 2, 9,
                                        bgcolor=color.new(color.black, 85),
                                        border_width=2,
                                        border_color=color.new(color.blue, 50))

    if barstate.islast
        // Calculate position sizing based on risk management
        accountBalance = 10000.0  // Default account size - user should adjust
        riskPercentage = 2.0      // Risk 2% per trade - conservative

        // Calculate position size based on stop loss distance
        stopDistance = if not na(currentStopLoss) and not na(currentEntryPrice)
            math.abs(currentEntryPrice - currentStopLoss)
        else
            atrValue * 1.0  // Default to 1 ATR if no specific stop

        riskAmount = accountBalance * (riskPercentage / 100)
        positionSize = stopDistance > 0 ? riskAmount / stopDistance : 0

        // Calculate lot size for forex (assuming XAUUSD)
        lotSize = positionSize / 100  // Standard lot calculation

        // Header
        table.cell(executionTable, 0, 0, "⚡ EXECUTION GUIDE",
                  text_color=color.white, text_size=size.normal,
                  bgcolor=color.new(color.blue, 60))
        table.cell(executionTable, 1, 0, "RISK MANAGEMENT",
                  text_color=color.white, text_size=size.normal,
                  bgcolor=color.new(color.blue, 60))

        // Order type
        table.cell(executionTable, 0, 1, "Order Type",
                  text_color=color.white, text_size=size.small)
        table.cell(executionTable, 1, 1, "MARKET ORDER",
                  text_color=color.yellow, text_size=size.small)

        // Entry timing
        table.cell(executionTable, 0, 2, "Entry Timing",
                  text_color=color.white, text_size=size.small)
        entryTiming = if lastSignalDirection == "BULLISH"
            "Enter when price breaks box top"
        else
            "Enter when price breaks box bottom"
        table.cell(executionTable, 1, 2, entryTiming,
                  text_color=color.lime, text_size=size.small)

        // Position sizing
        table.cell(executionTable, 0, 3, "Position Size",
                  text_color=color.white, text_size=size.small)
        positionText = str.tostring(lotSize, "#.##") + " lots"
        table.cell(executionTable, 1, 3, positionText,
                  text_color=color.yellow, text_size=size.small)

        // Risk amount
        table.cell(executionTable, 0, 4, "Risk Amount",
                  text_color=color.white, text_size=size.small)
        riskText = "$" + str.tostring(riskAmount, "#.##") + " (2%)"
        table.cell(executionTable, 1, 4, riskText,
                  text_color=color.orange, text_size=size.small)

        // Stop loss placement
        table.cell(executionTable, 0, 5, "Stop Loss",
                  text_color=color.white, text_size=size.small)
        stopText = if not na(currentStopLoss)
            str.tostring(currentStopLoss, "#.##")
        else
            "Calculate manually"
        table.cell(executionTable, 1, 5, stopText,
                  text_color=color.red, text_size=size.small)

        // Take profit
        table.cell(executionTable, 0, 6, "Take Profit",
                  text_color=color.white, text_size=size.small)
        tpText = if not na(currentTakeProfit)
            str.tostring(currentTakeProfit, "#.##")
        else
            "Calculate manually"
        table.cell(executionTable, 1, 6, tpText,
                  text_color=color.lime, text_size=size.small)

        // Risk:Reward verification
        table.cell(executionTable, 0, 7, "R:R Ratio",
                  text_color=color.white, text_size=size.small)
        rrVerification = if not na(riskReward)
            if riskReward >= 3.0
                str.tostring(riskReward, "#.#") + ":1 ✓"
            else if riskReward >= 2.0
                str.tostring(riskReward, "#.#") + ":1 ⚠"
            else
                str.tostring(riskReward, "#.#") + ":1 ✗"
        else
            "Calculate first"
        rrVerificationColor = if not na(riskReward)
            riskReward >= 3.0 ? color.lime : (riskReward >= 2.0 ? color.yellow : color.red)
        else
            color.gray
        table.cell(executionTable, 1, 7, rrVerification,
                  text_color=rrVerificationColor, text_size=size.small)

        // Execution checklist
        table.cell(executionTable, 0, 8, "Checklist",
                  text_color=color.white, text_size=size.small)
        checklistStatus = if goldEntryConfirmation and not na(currentEntryPrice) and not na(currentStopLoss)
            "✓ READY TO TRADE"
        else
            "⚠ VERIFY CONDITIONS"
        checklistColor = str.contains(checklistStatus, "✓") ? color.lime : color.yellow
        table.cell(executionTable, 1, 8, checklistStatus,
                  text_color=checklistColor, text_size=size.small)

// ——— XAUUSD Gold Performance Optimization Dashboard ————————————————————————————————————————
if enableGoldMode and showGoldDashboard
    // Create Gold-specific performance dashboard with improved readability
    var table goldTable = table.new(position.top_right, 3, 10,
                                   bgcolor=color.new(color.black, 80),
                                   border_width=1,
                                   border_color=color.new(color.yellow, 40))

    if barstate.islast
        // Header
        table.cell(goldTable, 0, 0, "🥇 GOLD MODE",
                  text_color=color.black, text_size=size.small,
                  bgcolor=color.new(color.yellow, 20))
        table.cell(goldTable, 1, 0, "VALUE",
                  text_color=color.black, text_size=size.small,
                  bgcolor=color.new(color.yellow, 20))
        table.cell(goldTable, 2, 0, "STATUS",
                  text_color=color.black, text_size=size.small,
                  bgcolor=color.new(color.yellow, 20))

        // Current session info
        table.cell(goldTable, 0, 1, "Session", text_color=color.white, text_size=size.tiny)
        table.cell(goldTable, 1, 1, currentSessionName, text_color=color.white, text_size=size.tiny)
        sessionStatusColor = sessionQuality >= 1.0 ? color.lime : (sessionQuality >= 0.8 ? color.yellow : color.red)
        table.cell(goldTable, 2, 1, str.tostring(sessionQuality, "#.#"), text_color=sessionStatusColor, text_size=size.tiny)

        // ATR multiplier info
        table.cell(goldTable, 0, 2, "ATR Multi", text_color=color.white, text_size=size.tiny)
        table.cell(goldTable, 1, 2, str.tostring(finalATRMultiplier, "#.#") + "x", text_color=color.white, text_size=size.tiny)
        multiplierColor = finalATRMultiplier >= 4.0 ? color.lime : (finalATRMultiplier >= 3.0 ? color.yellow : color.red)
        table.cell(goldTable, 2, 2, finalATRMultiplier >= 4.0 ? "HIGH" : (finalATRMultiplier >= 3.0 ? "MED" : "LOW"),
                  text_color=multiplierColor, text_size=size.tiny)

        // Volatility regime
        table.cell(goldTable, 0, 3, "Volatility", text_color=color.white, text_size=size.tiny)
        table.cell(goldTable, 1, 3, volatilityRegime, text_color=color.white, text_size=size.tiny)
        volatilityColorBright = switch volatilityRegime
            "HIGH" => color.red
            "LOW" => color.aqua
            => color.white
        table.cell(goldTable, 2, 3, str.tostring(volatilityRatio, "#.##"), text_color=volatilityColorBright, text_size=size.tiny)

        // Entry confirmation status
        table.cell(goldTable, 0, 4, "Entry Ready", text_color=color.white, text_size=size.tiny)
        table.cell(goldTable, 1, 4, goldEntryConfirmation ? "READY" : "BLOCKED",
                  text_color=goldEntryConfirmation ? color.lime : color.red, text_size=size.tiny)
        table.cell(goldTable, 2, 4, goldAntiWhipsawPass ? "✓" : "✗",
                  text_color=goldAntiWhipsawPass ? color.lime : color.red, text_size=size.tiny)

        // Volume analysis
        table.cell(goldTable, 0, 5, "Volume", text_color=color.white, text_size=size.tiny)
        table.cell(goldTable, 1, 5, volumeSpike ? "SPIKE" : "NORMAL",
                  text_color=volumeSpike ? color.lime : color.white, text_size=size.tiny)
        volumeRatio = volume / volumeAverage
        table.cell(goldTable, 2, 5, str.tostring(volumeRatio, "#.#") + "x",
                  text_color=volumeRatio >= 1.5 ? color.lime : color.white, text_size=size.tiny)

        // News time status
        table.cell(goldTable, 0, 6, "News", text_color=color.white, text_size=size.tiny)
        table.cell(goldTable, 1, 6, newsTimePass ? "CLEAR" : "AVOID",
                  text_color=newsTimePass ? color.lime : color.red, text_size=size.tiny)
        table.cell(goldTable, 2, 6, isHighImpactNewsTime ? "HIGH" : "SAFE",
                  text_color=isHighImpactNewsTime ? color.red : color.lime, text_size=size.tiny)

        // Risk-Reward calculation
        currentRR = enablePerformanceTracking and not na(lastSignalTarget) and not na(lastSignalPrice) ?
                   math.abs(lastSignalTarget - lastSignalPrice) / (atrValue * 1.0) : na
        table.cell(goldTable, 0, 7, "Risk:Reward", text_color=color.white, text_size=size.tiny)
        table.cell(goldTable, 1, 7, na(currentRR) ? "N/A" : str.tostring(currentRR, "#.#") + ":1",
                  text_color=color.white, text_size=size.tiny)
        rrColor = na(currentRR) ? color.white : (currentRR >= 3.0 ? color.lime : (currentRR >= 2.0 ? color.yellow : color.red))
        table.cell(goldTable, 2, 7, na(currentRR) ? "N/A" : (currentRR >= 3.0 ? "GOOD" : (currentRR >= 2.0 ? "OK" : "POOR")),
                  text_color=rrColor, text_size=size.tiny)

        // Session recommendation
        table.cell(goldTable, 0, 8, "Recommend", text_color=color.white, text_size=size.tiny)
        sessionRecommendation = switch
            isOverlapSession => "TRADE!"
            isLondonSession => "GOOD"
            isNewYorkSession => "OK"
            isAsianSession => "AVOID"
            => "CLOSED"
        table.cell(goldTable, 1, 8, sessionRecommendation, text_color=sessionStatusColor, text_size=size.tiny)
        table.cell(goldTable, 2, 8, str.tostring(sessionQuality * 100, "#") + "%", text_color=sessionStatusColor, text_size=size.tiny)

        // Overall Gold optimization status
        goldOptimizationScore = (goldEntryConfirmation ? 25 : 0) + (sessionQuality >= 0.8 ? 25 : 0) +
                               (finalATRMultiplier >= 3.0 ? 25 : 0) + (na(currentRR) ? 0 : (currentRR >= 3.0 ? 25 : 0))
        table.cell(goldTable, 0, 9, "Gold Score", text_color=color.white, text_size=size.tiny)
        scoreColor = goldOptimizationScore >= 75 ? color.lime : (goldOptimizationScore >= 50 ? color.yellow : color.red)
        table.cell(goldTable, 1, 9, str.tostring(goldOptimizationScore) + "%",
                  text_color=scoreColor, text_size=size.tiny)
        optimizationStatus = goldOptimizationScore >= 75 ? "OPTIMAL" : (goldOptimizationScore >= 50 ? "GOOD" : "POOR")
        table.cell(goldTable, 2, 9, optimizationStatus,
                  text_color=scoreColor, text_size=size.tiny)

// ——— Integration Test Dashboard ————————————————————————————————————————
if showIntegrationDashboard and enableIntegrationTests
    // Calculate integration test metrics
    integrationSuccessRate = integrationTestCount > 0 ? (integrationTestPassed / integrationTestCount) * 100 : 0

    // Create comprehensive integration test dashboard (positioned at bottom-center for better organization)
    var table integrationTable = table.new(position.bottom_center, 3, 10,
                                          bgcolor=color.new(color.black, 90),
                                          border_width=2,
                                          border_color=color.new(color.orange, 50))

    if barstate.islast
        // Header
        table.cell(integrationTable, 0, 0, "🧪 INTEGRATION TESTS",
                  text_color=color.white, text_size=size.normal,
                  bgcolor=color.new(color.orange, 70))
        table.cell(integrationTable, 1, 0, "STATUS",
                  text_color=color.white, text_size=size.normal,
                  bgcolor=color.new(color.orange, 70))
        table.cell(integrationTable, 2, 0, "DETAILS",
                  text_color=color.white, text_size=size.normal,
                  bgcolor=color.new(color.orange, 70))

        // Overall test summary
        table.cell(integrationTable, 0, 1, "Total Tests", text_color=color.white, text_size=size.small)
        table.cell(integrationTable, 1, 1, str.tostring(integrationTestCount), text_color=color.white, text_size=size.small)
        table.cell(integrationTable, 2, 1, "Passed: " + str.tostring(integrationTestPassed), text_color=color.green, text_size=size.small)

        table.cell(integrationTable, 0, 2, "Success Rate", text_color=color.white, text_size=size.small)
        table.cell(integrationTable, 1, 2, str.tostring(integrationSuccessRate, "#.#") + "%",
                  text_color=integrationSuccessRate >= 90 ? color.green : (integrationSuccessRate >= 70 ? color.orange : color.red),
                  text_size=size.small)
        table.cell(integrationTable, 2, 2, "Failed: " + str.tostring(integrationTestFailed), text_color=color.red, text_size=size.small)

        // Individual component tests
        table.cell(integrationTable, 0, 3, "Signal-Performance", text_color=color.white, text_size=size.tiny)
        table.cell(integrationTable, 1, 3, signalPerformanceSyncOK ? "✅ PASS" : "❌ FAIL",
                  text_color=signalPerformanceSyncOK ? color.green : color.red, text_size=size.tiny)
        table.cell(integrationTable, 2, 3, lastSignalTriggeredTracking ? "Sync OK" : "No Sync",
                  text_color=lastSignalTriggeredTracking ? color.green : color.gray, text_size=size.tiny)

        table.cell(integrationTable, 0, 4, "Filter Interactions", text_color=color.white, text_size=size.tiny)
        table.cell(integrationTable, 1, 4, filterInteractionOK ? "✅ PASS" : "❌ FAIL",
                  text_color=filterInteractionOK ? color.green : color.red, text_size=size.tiny)
        filterCount = (enableTrendFilter ? 1 : 0) + (enableRangeFilter ? 1 : 0) + (enableStrengthFilter ? 1 : 0)
        table.cell(integrationTable, 2, 4, str.tostring(filterCount) + " Active", text_color=color.white, text_size=size.tiny)

        table.cell(integrationTable, 0, 5, "Visual-Data Sync", text_color=color.white, text_size=size.tiny)
        table.cell(integrationTable, 1, 5, visualDataSyncOK ? "✅ PASS" : "❌ FAIL",
                  text_color=visualDataSyncOK ? color.green : color.red, text_size=size.tiny)
        table.cell(integrationTable, 2, 5, lastTrackingTriggeredVisuals ? "Lines OK" : "No Lines",
                  text_color=lastTrackingTriggeredVisuals ? color.green : color.gray, text_size=size.tiny)

        table.cell(integrationTable, 0, 6, "State Consistency", text_color=color.white, text_size=size.tiny)
        table.cell(integrationTable, 1, 6, stateConsistencyOK ? "✅ PASS" : "❌ FAIL",
                  text_color=stateConsistencyOK ? color.green : color.red, text_size=size.tiny)
        table.cell(integrationTable, 2, 6, lastSignalActive ? "Active" : "Idle",
                  text_color=lastSignalActive ? color.orange : color.gray, text_size=size.tiny)

        // Component health indicators
        table.cell(integrationTable, 0, 7, "Component Health", text_color=color.yellow, text_size=size.small)
        overallHealth = signalPerformanceSyncOK and filterInteractionOK and visualDataSyncOK and stateConsistencyOK
        table.cell(integrationTable, 1, 7, overallHealth ? "🟢 HEALTHY" : "🔴 ISSUES",
                  text_color=overallHealth ? color.green : color.red, text_size=size.small)
        table.cell(integrationTable, 2, 7, "", text_color=color.white, text_size=size.tiny)

        // Last error (if any)
        table.cell(integrationTable, 0, 8, "Last Error", text_color=color.white, text_size=size.tiny)
        table.cell(integrationTable, 1, 8, na(lastIntegrationError) ? "None" : "Error",
                  text_color=na(lastIntegrationError) ? color.green : color.red, text_size=size.tiny)
        table.cell(integrationTable, 2, 8, na(lastIntegrationError) ? "All Good" : "Check Log",
                  text_color=na(lastIntegrationError) ? color.green : color.red, text_size=size.tiny)

        // Integration flow status
        table.cell(integrationTable, 0, 9, "Data Flow", text_color=color.white, text_size=size.tiny)
        dataFlowOK = lastSignalTriggeredTracking and lastTrackingTriggeredVisuals
        table.cell(integrationTable, 1, 9, dataFlowOK ? "✅ FLOWING" : "⚠️ BLOCKED",
                  text_color=dataFlowOK ? color.green : color.orange, text_size=size.tiny)
        table.cell(integrationTable, 2, 9, "Signal→Track→Visual", text_color=color.gray, text_size=size.tiny)

// ——— Professional Performance Display System ————————————————————————————————————————
if showPerformanceTable and enablePerformanceTracking
    // Calculate performance metrics
    successRate = totalSignals > 0 ? (successfulSignals / totalSignals) * 100 : 0

    // Create professional performance table with clear definitions
    var table perfTable = table.new(position.bottom_right, 2, 8,
                                   bgcolor=color.new(professionalMode ? color.navy : color.white, 95),
                                   border_width=1,
                                   border_color=color.new(color.gray, 70))

    if barstate.islast
        // Header
        table.cell(perfTable, 0, 0, "📊 REAL-TIME PERFORMANCE",
                  text_color=professionalMode ? color.white : color.black,
                  text_size=size.small,
                  bgcolor=color.new(professionalMode ? color.navy : color.blue, 80))
        table.cell(perfTable, 1, 0, "LIVE TRACKING",
                  text_color=professionalMode ? color.white : color.black,
                  text_size=size.small,
                  bgcolor=color.new(professionalMode ? color.navy : color.blue, 80))

        // Total signals with clear definition
        table.cell(perfTable, 0, 1, "Total Signals",
                  text_color=professionalMode ? color.white : color.black, text_size=size.tiny)
        signalCountText = str.tostring(totalSignals) + " (Live)"
        table.cell(perfTable, 1, 1, signalCountText,
                  text_color=professionalMode ? color.white : color.black, text_size=size.tiny)

        // Success rate with clear definition
        table.cell(perfTable, 0, 2, "Target Hit Rate",
                  text_color=professionalMode ? color.white : color.black, text_size=size.tiny)
        // Add clarification that this measures target achievement, not profit
        successRateText = if totalSignals > 0
            str.tostring(successRate, "#.#") + "% (TP Hit)"
        else
            "No data yet"
        table.cell(perfTable, 1, 2, successRateText,
                  text_color=successRate >= 60 ? color.green : (successRate >= 40 ? color.orange : color.red),
                  text_size=size.tiny)

        // Last signal info
        table.cell(perfTable, 0, 3, "Last Signal",
                  text_color=professionalMode ? color.white : color.black, text_size=size.tiny)
        table.cell(perfTable, 1, 3, na(lastSignalDirection) ? "None" : lastSignalDirection,
                  text_color=lastSignalDirection == "BULLISH" ? color.green : (lastSignalDirection == "BEARISH" ? color.red : color.gray),
                  text_size=size.tiny)

        // Signal status
        table.cell(perfTable, 0, 4, "Status",
                  text_color=professionalMode ? color.white : color.black, text_size=size.tiny)
        signalStatus = lastSignalActive ? "ACTIVE" : (lastSignalSuccess ? "SUCCESS" : "CLOSED")
        table.cell(perfTable, 1, 4, signalStatus,
                  text_color=lastSignalActive ? color.orange : (lastSignalSuccess ? color.green : color.gray),
                  text_size=size.tiny)

        // Target level
        table.cell(perfTable, 0, 5, "Current Target",
                  text_color=professionalMode ? color.white : color.black, text_size=size.tiny)
        table.cell(perfTable, 1, 5, na(lastSignalTarget) ? "No active signal" : str.tostring(lastSignalTarget, "#.##"),
                  text_color=professionalMode ? color.white : color.black, text_size=size.tiny)

        // Measurement criteria explanation
        table.cell(perfTable, 0, 6, "Criteria",
                  text_color=professionalMode ? color.white : color.black, text_size=size.tiny)
        table.cell(perfTable, 1, 6, "Price reaches TP level",
                  text_color=color.yellow, text_size=size.tiny)

        // Data type disclaimer
        table.cell(perfTable, 0, 7, "Data Type",
                  text_color=professionalMode ? color.white : color.black, text_size=size.tiny)
        table.cell(perfTable, 1, 7, "FORWARD TEST ONLY",
                  text_color=color.orange, text_size=size.tiny)

// ——— Visual Signal Target Lines (Global Scope for Pine Script v5 Compliance) ————————————————————————————————————————
// Calculate conditional series for target line display
showTargetLine = enablePerformanceTracking and lastSignalActive and not na(lastSignalTarget)
targetLineValue = showTargetLine ? lastSignalTarget : na

// Calculate conditional series for entry line display
showEntryLine = enablePerformanceTracking and lastSignalActive and not na(lastSignalPrice)
entryLineValue = showEntryLine ? lastSignalPrice : na

// Target line color based on signal direction
targetLineColor = showTargetLine ?
                 (lastSignalDirection == "BULLISH" ?
                  color.new(color.green, professionalMode ? 80 : 70) :
                  color.new(color.red, professionalMode ? 80 : 70)) : na

// Entry line color
entryLineColor = showEntryLine ? color.new(color.blue, professionalMode ? 85 : 75) : na

// Plot target line (global scope)
plot(targetLineValue,
     title="Signal Target",
     color=targetLineColor,
     linewidth=professionalMode ? 1 : 2,
     style=plot.style_linebr,
     display=display.all)

// Plot entry price line (global scope)
plot(entryLineValue,
     title="Signal Entry",
     color=entryLineColor,
     linewidth=1,
     style=plot.style_linebr,
     display=display.all)

// ——— Integration Test: Visual-Data Synchronization ————————————————————————————————————————
if enableIntegrationTests and enablePerformanceTracking
    integrationTestCount += 1

    // Test 4: Verify visual elements sync with data state
    visualDataTestPassed = true

    // Check target line visibility matches signal state
    expectedTargetVisible = lastSignalActive and not na(lastSignalTarget)
    actualTargetVisible = showTargetLine
    if expectedTargetVisible != actualTargetVisible
        visualDataTestPassed := false
        lastIntegrationError := "Target line visibility doesn't match signal state"

    // Check entry line visibility matches signal state
    expectedEntryVisible = lastSignalActive and not na(lastSignalPrice)
    actualEntryVisible = showEntryLine
    if expectedEntryVisible != actualEntryVisible
        visualDataTestPassed := false
        lastIntegrationError := "Entry line visibility doesn't match signal state"

    // Check target line value matches tracking data
    if showTargetLine and targetLineValue != lastSignalTarget
        visualDataTestPassed := false
        lastIntegrationError := "Target line value doesn't match tracking data"

    if visualDataTestPassed
        visualDataSyncOK := true
        lastTrackingTriggeredVisuals := true
        integrationTestPassed += 1
    else
        visualDataSyncOK := false
        integrationTestFailed += 1

//---------------------------------------------------------------------------------------------------------------------
// Settings 
//---------------------------------------------------------------------------------------------------------------------{

// Static display and input settings (use var for performance)
var display  = display.all - display.status_line
var brpType = input.string("All", "Pattern Type", options = ["Normal", "Enhanced", "All"], display = display)
var brpSR  = input.string("Level", "Derived Support and Resistance", options = ["Level", "Zone", "None"], display = display)
var brpAC  = input.color(#2962ff, 'Bullish Reversal Patterns')
var brpSC  = input.color(#ff9800, 'Bearish Reversal Patterns')

var trendIndiGroup = 'Trend Filtering'
var trendType = input.string("None", "Filtering", options = ["Moving Average Cloud", "Supertrend", "Donchian Channels", "None"], group = trendIndiGroup, inline = 'flt', display = display)
var trendFilt = input.string("Aligned", "", options = ["Aligned", "Opposite"], group = trendIndiGroup, inline = 'flt', display = display) // options = ["Aligned", "Opposite", "No detection"]
var trendAC  = input.color(#089981, 'Bullish Trend', inline = 'trnd')
var trendSC  = input.color(#f23645, ' Bearish Trend', inline = 'trnd')

var ma_Group  = 'Moving Average Settings'
var maType    = input.string("HMA", "Type", options = ["SMA", "EMA", "HMA", "RMA", "WMA", "VWMA"], group = ma_Group, display = display)
var maFLength  = input.int(50, 'Fast Length', minval = 1, maxval = 100, group = ma_Group, display = display)
var maSLength  = input.int(200, 'Slow Length', minval = 100, group = ma_Group, display = display)

var st_Group  = 'Supertrend Settings'
var atrPeriod = input.int(10, 'ATR Length', minval=1, group = st_Group, display = display)
var factor = input.float(3, 'Factor', minval = 2, step = 0.1, group = st_Group, display = display)

var dc_Group  = 'Donchian Channel Settings'
var length = input.int(13, 'Length', minval = 1, group = dc_Group, display = display)

//---------------------------------------------------------------------------------------------------------------------}
// Functions / Methods
//---------------------------------------------------------------------------------------------------------------------{

movingAverage(source, length, maType) => 
    switch maType
        "SMA"  => ta.sma (source, length)
        "EMA"  => ta.ema (source, length)
        "HMA"  => ta.hma (source, length)
        "RMA"  => ta.rma (source, length)
        "WMA"  => ta.wma (source, length)
        "VWMA" => ta.vwma(source, length)

donchian(len) => math.avg(ta.lowest(len), ta.highest(len))

// Enhanced reversal patterns using more reliable close/open data
isBullishReversal() =>
    // Bar 2: Bearish candle
    bar2Bearish = close[2] < open[2]
    // Bar 1: Inside bar with bearish close (more reliable than high/low comparison)
    bar1Inside = close[1] < open[1] and close[1] < close[2] and open[1] < open[2]
    // Current bar: Bullish candle breaking above previous structure
    currentBullish = close > open and close > math.max(close[2], open[2])
    // Combine all conditions
    bar2Bearish and bar1Inside and currentBullish

isBearishReversal() =>
    // Bar 2: Bullish candle
    bar2Bullish = close[2] > open[2]
    // Bar 1: Inside bar with bullish close (more reliable than high/low comparison)
    bar1Inside = close[1] > open[1] and close[1] > close[2] and open[1] > open[2]
    // Current bar: Bearish candle breaking below previous structure
    currentBearish = close < open and close < math.min(close[2], open[2])
    // Combine all conditions
    bar2Bullish and bar1Inside and currentBearish

//---------------------------------------------------------------------------------------------------------------------}
// Calculations - Trend Indicators - Moving Average Cloud
//---------------------------------------------------------------------------------------------------------------------{

maFast = movingAverage(close, maFLength, maType) 
maSlow = movingAverage(close, maSLength, maType) 

maColor = maFast > maSlow ? trendAC : trendSC
ma1 = plot(trendType == 'Moving Average Cloud' ? maFast : na, "ma fast", color.new(maColor, 81), 1, plot.style_linebr, display = display, editable = false)
ma2 = plot(trendType == 'Moving Average Cloud' ? maSlow : na, "ma slow", color.new(maColor, 73), 1, plot.style_linebr, display = display, editable = false)

fill(ma1, ma2, math.max(maFast, maSlow), math.min(maFast, maSlow), color.new(maColor, maFast > maSlow ? 99 : 81), color.new(maColor, maFast > maSlow ? 81 : 99))

//---------------------------------------------------------------------------------------------------------------------}
// Calculations - Trend Indicators - Supertrend
//---------------------------------------------------------------------------------------------------------------------{

[supertrend, direction] = ta.supertrend(factor, atrPeriod)

supertrend := barstate.isfirst ? na : supertrend
upTrend     = plot(direction < 0 ? trendType == 'Supertrend' ? supertrend : na : na, "Up Trend", color.new(trendAC, 73), style = plot.style_linebr, display = display, editable = false)
downTrend   = plot(direction < 0 ? na : trendType == 'Supertrend' ? supertrend : na, "Down Trend", color.new(trendSC, 73),   style = plot.style_linebr, display = display, editable = false)
bodyMiddle  = plot(barstate.isfirst ? na : trendType == 'Supertrend' ? (open + close) / 2 : na, "Body Middle", display = display.none, editable = false)

fill(bodyMiddle, upTrend  , supertrend, (open + close) / 2, color.new(trendAC, 81), color.new(chart.bg_color, 100), fillgaps = false)
fill(bodyMiddle, downTrend, (open + close) / 2, supertrend, color.new(chart.bg_color, 100), color.new(trendSC, 81), fillgaps = false)

//---------------------------------------------------------------------------------------------------------------------}
// Calculations - Trend Indicators - Donchian Channels
//---------------------------------------------------------------------------------------------------------------------{

var os = 0
upper = ta.highest(close, length)
lower = ta.lowest(close, length)
os := upper > upper[1] ? 1 : lower < lower[1] ? 0 : os

dcUpper = plot(trendType == 'Donchian Channels' ? upper : na, color = os == 1 ? color.new(trendAC, 99) : color.new(trendSC, 73), display = display, editable = false)
dcLower = plot(trendType == 'Donchian Channels' ? lower : na, color = os == 1 ? color.new(trendAC, 73) : color.new(trendSC, 99), display = display, editable = false)

fill(dcUpper, dcLower, upper, lower, os == 1 ? color.new(chart.bg_color, 100) : color.new(trendSC, 81) , os == 0 ? color.new(chart.bg_color, 100) : color.new(trendAC, 81))

//---------------------------------------------------------------------------------------------------------------------}
// Calculations - 3-Bar Reversal Pattern
//---------------------------------------------------------------------------------------------------------------------{

C_DownTrend = true
C_UpTrend = true

if trendType == 'Moving Average Cloud'
    if trendFilt == 'Aligned'
	    C_DownTrend := close < maFast and maFast < maSlow
	    C_UpTrend := close > maFast and maFast > maSlow
    else if trendFilt == 'Opposite'
	    C_DownTrend := close > maFast and maFast > maSlow
	    C_UpTrend := close < maFast and maFast < maSlow
    else
        C_DownTrend := true
        C_UpTrend := true

if trendType == 'Supertrend'
    if trendFilt == 'Aligned'
        C_DownTrend := direction > 0 
        C_UpTrend := direction < 0 
    else if trendFilt == 'Opposite'
        C_DownTrend := direction < 0 
        C_UpTrend := direction > 0 
    else
        C_DownTrend := true
        C_UpTrend := true

if trendType == 'Donchian Channels'
    if trendFilt == 'Aligned'
        C_DownTrend := os == 0 
        C_UpTrend := os == 1 
    else if trendFilt == 'Opposite'
        C_DownTrend := os == 1
        C_UpTrend := os == 0 
    else
        C_DownTrend := true
        C_UpTrend := true

// ——— Market Quality Assessment ————————————————————————————————————————
// HIGH PRIORITY: Trend Uncertainty Detection
if enableTrendFilter
    if trendType == 'Moving Average Cloud'
        // Detect when MAs are too close together (converging)
        maSpread = math.abs(maFast - maSlow) / close * 100
        trendUncertain := maSpread < trendUncertaintyThreshold
    else if trendType == 'Supertrend'
        // Detect frequent direction changes (whipsaw)
        directionChanges = 0
        for i = 1 to 5
            if direction[i] != direction[i-1]
                directionChanges += 1
        trendUncertain := directionChanges >= 2
    else if trendType == 'Donchian Channels'
        // Detect when channels are too narrow
        channelWidth = (upper - lower) / close * 100
        trendUncertain := channelWidth < trendUncertaintyThreshold
    else
        trendUncertain := false
else
    trendUncertain := false

// MEDIUM PRIORITY: Range-Bound Market Detection
if enableRangeFilter and not na(pivotHighLevel) and not na(pivotLowLevel)
    rangeSize = (pivotHighLevel - pivotLowLevel) / close * 100
    // Check if price broke out significantly (>2%)
    significantBreakout = close > pivotHighLevel * 1.02 or close < pivotLowLevel * 0.98

    if significantBreakout
        consolidationCount := 0  // Reset on breakout
    else
        consolidationCount += 1

    // Range-bound if: small range AND been consolidating
    isRangeBound := rangeSize < 2.0 and consolidationCount >= consolidationBars
else
    isRangeBound := false

// MEDIUM PRIORITY: Breakout Strength Validation
var bool weakBreakout = false
if enableStrengthFilter
    // Calculate average candle size over last 10 bars
    avgCandleSize = 0.0
    for i = 1 to 10
        avgCandleSize += math.abs(close[i] - open[i])
    avgCandleSize := avgCandleSize / 10

    // Current candle size
    currentCandleSize = math.abs(close - open)

    // Weak if current candle is smaller than required multiple of average
    weakBreakout := currentCandleSize < (avgCandleSize * strengthMultiplier)
else
    weakBreakout := false

// Overall market quality assessment
poorMarketQuality := trendUncertain or isRangeBound or weakBreakout

// Make pattern detection optional to reduce visual clutter and focus on trend direction
bullishReversal = showPatterns and isBullishReversal() and C_UpTrend and not poorMarketQuality
bearishReversal = showPatterns and isBearishReversal() and C_DownTrend and not poorMarketQuality

var line lnAT = na
var line lnAB = na
var line lnAT2 = na
var line lnAB2 = na
var label lbAT = na
var box bxA = na
var bool bullProcess = false
var bool bullProcess2 = false
var float bullHigh = na

if bullishReversal and (brpType == 'All' ? true : brpType == 'Enhanced' ? close > high[2] ? true : false : brpType == 'Normal' ? close < high[2] ? true : false : false)
    bullProcess := true

    lbAT := label.new(bar_index, low, '▲', color = color(na), textcolor = color.new(brpAC, 07), style = label.style_label_up, size = size.small, tooltip = 'new bullish pattern detected' + (close > high[2] ? ' (enchanced)' : ' (normal)'))

    lnAT := line.new(bar_index[2], high[2], bar_index, high[2], color = color.new(brpAC, 53))
    lnAB := line.new(bar_index[1], math.min(low[1], low), bar_index[0], math.min(low[1], low), color = color.new(brpAC, 53))
    linefill.new(lnAT, lnAB, color.new(brpAC, 73))

    lnAT2 := line.new(bar_index[2], high[2], bar_index, high[2], color = color.new(brpAC, 53))
    lnAB2 := line.new(bar_index[1], math.min(low[1], low), bar_index[0], math.min(low[1], low), color = color.new(brpAC, 53))

    bullHigh := brpSR == 'Zone' ? math.max(low[1], low) : math.min(low[1], low)

if bullProcess 
    if close[1] > lnAT.get_price(bar_index)
        if bullProcess[1] and bullProcess[1] != bullProcess[2]
            lbAT.set_tooltip('enchanced pattern (confirmed at detection)\nprice activity above the pattern high')
        else
            lbAT.set_tooltip('pattern confirmed ' + str.tostring(bar_index[1] - lbAT.get_x()) + ' bars later')
            label.new(bar_index[1], low[1], '⦁', color = color(na), textcolor = color.new(brpAC, 07), style = label.style_label_up, size = size.small, tooltip = 'confirmation bar\nprice activity above the pattern high')
        
        bullProcess := false

        bxA := box.new(bar_index, bullHigh, bar_index, lnAB.get_price(bar_index), color.new(brpAC, brpSR == 'Zone' ? 73 : 53), bgcolor = color.new(brpAC, 73))
        bullProcess2 := true

    if close[1] < lnAB.get_price(bar_index) or bearishReversal
        lbAT.set_tooltip('pattern failed\nthe low of the pattern breached')
        bullProcess := false

    if not bullProcess 
        lnAT2.set_x2(bar_index[1])
        lnAB2.set_x2(bar_index[1])
    else
        lnAT2.set_x2(bar_index)
        lnAB2.set_x2(bar_index)

if bullProcess2 and brpSR != 'None'
    bxA.set_right(bar_index)
    if close <= bxA.get_bottom()
        bullProcess2 := false


var line lnST = na
var line lnSB = na
var line lnST2 = na
var line lnSB2 = na
var label lbST = na
var box bxS = na
var bool bearProcess = false
var bool bearProcess2 = false
var float bearLow = na

if bearishReversal and (brpType == 'All' ? true : brpType == 'Enhanced' ? close < low[2] ? true : false : brpType == 'Normal' ? close > low[2] ? true : false : false)
    bearProcess := true

    lbST := label.new(bar_index, high, '▼', color = color(na), textcolor = color.new(brpSC, 07), style = label.style_label_down, size = size.small, tooltip = 'new bearish pattern detected' + (close < low[2] ? ' (enchanced)' : ' (normal)'))

    lnSB := line.new(bar_index[2], low[2], bar_index, low[2], color = color.new(brpSC, 53))
    lnST := line.new(bar_index[1], math.max(high[1], high), bar_index[0], math.max(high[1], high), color = color.new(brpSC, 53))
    linefill.new(lnST, lnSB, color.new(brpSC, 73))

    lnSB2 := line.new(bar_index[2], low[2], bar_index, low[2], color = color.new(brpSC, 53))
    lnST2 := line.new(bar_index[1], math.max(high[1], high), bar_index[0], math.max(high[1], high), color = color.new(brpSC, 53))

    bearLow := brpSR == 'Zone' ? math.min(high[1], high) : math.max(high[1], high)

if bearProcess 
    if close[1] > lnST.get_price(bar_index) or bullishReversal
        lbST.set_tooltip('pattern failed\nthe high of the pattern breached')
        bearProcess := false

    if close[1] < lnSB.get_price(bar_index) 
        if bearProcess[1] and bearProcess[1] != bearProcess[2]
            lbST.set_tooltip('enchanced pattern (confirmed at detection)\nprice activity below the pattern low')
        else
            lbST.set_tooltip('pattern confirmed ' + str.tostring(bar_index[1] - lbST.get_x()) + ' bars later')
            label.new(bar_index[1], high[1], '⦁', color = color(na), textcolor = color.new(brpSC, 07), style = label.style_label_down, size = size.small, tooltip = 'confirmation bar\nprice activity blow the pattern low')

        bearProcess := false

        bxS := box.new(bar_index, lnST.get_price(bar_index), bar_index, bearLow, color.new(brpSC, brpSR == 'Zone' ? 73 : 53), bgcolor = color.new(brpSC, 73))
        bearProcess2 := true

    if not bearProcess 
        lnST2.set_x2(bar_index[1])
        lnSB2.set_x2(bar_index[1])
    else
        lnST2.set_x2(bar_index)
        lnSB2.set_x2(bar_index)

if bearProcess2 and brpSR != 'None'
    bxS.set_right(bar_index)
    if close >= bxS.get_top()
        bearProcess2 := false

// ——— Professional Market Quality Warning System ————————————————————————————————————————
// EXTREMELY SUBTLE: Professional-grade visual warnings that don't interfere with trading

// Professional background warnings (ultra-subtle, must be at global scope)
professionalWarningColor = professionalMode ?
     color.new(color.gray, warningIntensity) :
     color.new(color.yellow, 95)

// Trend uncertainty: Extremely subtle gray background (98% transparency by default)
bgcolor(showWarnings and trendUncertain and professionalMode ?
     color.new(color.gray, warningIntensity) :
     (showWarnings and trendUncertain ? color.new(color.yellow, 95) : na),
     title="Trend Uncertain")

// Range-bound: Even more subtle (99% transparency)
bgcolor(showWarnings and isRangeBound ?
     color.new(color.gray, math.min(warningIntensity + 1, 99)) : na,
     title="Range Bound Market")

// Professional warning indicator (minimal visual impact)
if showWarnings and poorMarketQuality and not professionalMode
    // Only show warning labels in non-professional mode
    label.new(
         bar_index,
         high * 1.0005,  // Closer to price for less visual disruption
         "⚠",
         color = color.new(color.gray, 90),  // Much more subtle
         textcolor = color.new(color.white, 30),  // Very faint text
         style = label.style_label_down,
         size = size.tiny,
         tooltip = "Market quality warning:\n" +
                  (trendUncertain ? "• Trend direction unclear\n" : "") +
                  (isRangeBound ? "• Range-bound conditions\n" : "") +
                  "Consider reduced position sizing"
     )

// Professional mode: No visual labels, only background hints
// This maintains clean chart appearance while providing subtle feedback

//---------------------------------------------------------------------------------------------------------------------}